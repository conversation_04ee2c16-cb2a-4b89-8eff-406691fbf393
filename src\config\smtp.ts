import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// SMTP server configuration
const smtpConfig = {
  // Server settings
  port: parseInt(process.env.SMTP_PORT || '25'),
  // Support both server-specific host variables and legacy variable
  host: (() => {
    // If SMTP_HOST_1 and SMTP_HOST_2 are defined, use the appropriate one
    if (process.env.SMTP_HOST_1) {
      // If this is server 2 (determined by environment variable)
      if (process.env.SMTP_SERVER_ID === '2' && process.env.SMTP_HOST_2) {
        return process.env.SMTP_HOST_2;
      }
      // Default to server 1
      return process.env.SMTP_HOST_1;
    }
    // Fallback to legacy variable or localhost if not set
    return process.env.SMTP_HOST || 'localhost';
  })(),

  // Security settings
  secure: process.env.SMTP_SECURE === 'true',

  // Authentication settings
  authOptional: process.env.SMTP_AUTH_OPTIONAL === 'true',

  // Size limits
  size: parseInt(process.env.SMTP_MAX_SIZE || '10485760'), // 10MB default

  // Connection limits
  maxClients: parseInt(process.env.SMTP_MAX_CLIENTS || '20'),

  // Allowed domains (comma-separated list)
  // Support both new server-specific variables and legacy variable
  allowedDomains: (() => {
    // First try to get domains from server-specific variables
    const domainsServer1 = (process.env.SMTP_ALLOWED_DOMAINS_SV1 || '')
      .split(',')
      .map(domain => domain.trim())
      .filter(Boolean);

    const domainsServer2 = (process.env.SMTP_ALLOWED_DOMAINS_SV2 || '')
      .split(',')
      .map(domain => domain.trim())
      .filter(Boolean);

    // If either server has domains, use those
    if (domainsServer1.length > 0 || domainsServer2.length > 0) {
      // Determine which server this instance is (based on SMTP_HOST)
      const isServer1 = !process.env.SMTP_HOST_2 || process.env.SMTP_HOST === process.env.SMTP_HOST_1;

      // Return domains for the appropriate server
      return isServer1 ? domainsServer1 : domainsServer2;
    }

    // Fallback to legacy variable if no server-specific domains are defined
    return (process.env.SMTP_ALLOWED_DOMAINS || '')
      .split(',')
      .map(domain => domain.trim())
      .filter(Boolean);
  })(),

  // Disable DNS validation in development
  disableDnsValidation: process.env.NODE_ENV !== 'production',

  // Logging
  logger: process.env.SMTP_LOGGING === 'true',
};

export default smtpConfig;
