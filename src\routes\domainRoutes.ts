import express from 'express';
import * as domainController from '../controllers/domainController';
import { generalRateLimiter } from '../middlewares/rateLimiter';
import { rapidApiRateLimiter } from '../middlewares/rapidApiRateLimiter';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';
import { cacheMiddleware } from '../middlewares/cacheMiddleware';

const router = express.Router();

// PHASE 2: Streamlined middleware for high-traffic domains endpoint
// Reduced middleware chain for better performance
router.use(rapidApiRateLimiter);
// PHASE 2: Skip general rate limiter for domains endpoint (already covered by rapidApiRateLimiter)
// router.use(generalRateLimiter); // Commented out to reduce middleware overhead
router.use(rapidApiAuth);

// GET /api/domains - Get all domains
// PHASE 2: Optimized cache middleware with improved key generation
router.get('/', cacheMiddleware(604800, req => {
  // PHASE 2: Improved cache key generation including query parameters
  const rapidApiKey = req.headers['x-rapidapi-key'] || 'public';
  const queryString = Object.keys(req.query).length > 0 ? JSON.stringify(req.query) : '';
  return `domains:all:${rapidApiKey}:${queryString}`;
}), domainController.getDomains);

export default router;
