import * as fs from 'fs';
import * as path from 'path';
import serverConfig from '../config/server';
import { createStream } from 'rotating-file-stream';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create rotating streams
const accessLogStream = createStream('access.log', {
  interval: '1d', // Rotate daily
  path: logsDir,
  size: '10M', // Rotate when size exceeds 10MB
  compress: 'gzip', // Compress rotated logs
  maxFiles: 14 // Keep logs for 14 days
});

const errorLogStream = createStream('error.log', {
  interval: '1d',
  path: logsDir,
  size: '10M',
  compress: 'gzip',
  maxFiles: 30 // Keep error logs longer
});

/**
 * Format a log message with timestamp and request ID if available
 */
const formatLogMessage = (level: string, message: string, requestId?: string): string => {
  const timestamp = new Date().toISOString();
  const requestIdStr = requestId ? ` [${requestId}]` : '';
  return `[${timestamp}] [${level}]${requestIdStr} ${message}`;
};

/**
 * Write to rotating log stream
 */
const writeToLog = (stream: any, message: string): void => {
  stream.write(message + '\n');
};

/**
 * Log levels
 */
export const logger = {
  /**
   * Log info message
   */
  info: (message: string, requestId?: string): void => {
    const formattedMessage = formatLogMessage('INFO', message, requestId);
    console.log(formattedMessage);
    writeToLog(accessLogStream, formattedMessage);
  },

  /**
   * Log warning message
   */
  warn: (message: string, requestId?: string): void => {
    const formattedMessage = formatLogMessage('WARN', message, requestId);
    console.warn(formattedMessage);
    writeToLog(accessLogStream, formattedMessage);
  },

  /**
   * Log error message
   */
  error: (message: string, error?: Error, requestId?: string): void => {
    const formattedMessage = formatLogMessage('ERROR', message, requestId);
    console.error(formattedMessage);

    // Log stack trace in development mode or to file in any mode
    if (error?.stack) {
      const stackMessage = formatLogMessage('STACK', error.stack, requestId);

      // Always log stack to file
      writeToLog(errorLogStream, formattedMessage);
      writeToLog(errorLogStream, stackMessage);

      // Only log stack to console in development
      if (serverConfig.isDevelopment) {
        console.error(error.stack);
      }
    } else {
      writeToLog(errorLogStream, formattedMessage);
    }
  },

  /**
   * Log HTTP request
   */
  http: (req: any, res: any, responseTime?: number): void => {
    const { method, originalUrl, ip } = req;
    const statusCode = res.statusCode;
    const userAgent = req.get('user-agent') || '-';
    const timeString = responseTime ? ` - ${responseTime}ms` : '';

    // Get request ID from response headers if available
    const requestId = res.get('X-Request-ID') || undefined;

    const message = `${method} ${originalUrl} ${statusCode} - ${ip} - ${userAgent}${timeString}`;
    const formattedMessage = formatLogMessage('HTTP', message, requestId);

    console.log(formattedMessage);
    writeToLog(accessLogStream, formattedMessage);
  },
};

export default logger;
