import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';
import logger from '../utils/logger';
import { writePool, readPool, getDatabaseConfig } from './database-pools';
import { executeQuery, getClient as getRouterClient } from '../utils/databaseRouter';

// Load environment variables
dotenv.config();

// Track active clients for backward compatibility
const activeClients = new Set<PoolClient>();

// Get database configuration
const dbConfig = getDatabaseConfig();

// Use write pool as the default pool for backward compatibility
const pool = writePool;

// Log database configuration
logger.info('Database configuration loaded:');
if (dbConfig.hasReadWriteSplitting) {
  logger.info(`- Write database: ${dbConfig.writeConfig.host}:${dbConfig.writeConfig.port}/${dbConfig.writeConfig.database}`);
  logger.info(`- Read database: ${dbConfig.readConfig?.host}:${dbConfig.readConfig?.port}/${dbConfig.readConfig?.database}`);
} else {
  logger.info(`- Single database: ${dbConfig.writeConfig.host}:${dbConfig.writeConfig.port}/${dbConfig.writeConfig.database}`);
}

// Add connection event logging for backward compatibility
pool.on('connect', (client: PoolClient) => {
  activeClients.add(client);
  if (process.env.NODE_ENV !== 'production') {
    logger.debug(`Legacy pool client connected. Active connections: ${activeClients.size}`);
  }
});

pool.on('remove', (client: PoolClient) => {
  activeClients.delete(client);
  if (process.env.NODE_ENV !== 'production') {
    logger.debug(`Legacy pool client removed. Active connections: ${activeClients.size}`);
  }
});

// Monitor connection pool less frequently
setInterval(() => {
  const idleCount = pool.idleCount;
  const totalCount = pool.totalCount;
  const waitingCount = pool.waitingCount;

  // Only log if pool is near capacity
  if (totalCount > pool.options.max * 0.8) {
    logger.warn(`DB Pool near capacity: ${totalCount}/${pool.options.max}`);
  }
}, 300000); // Check every 5 minutes instead of every minute

// Enhanced error handling
pool.on('error', (err: Error, client: PoolClient) => {
  logger.error('Unexpected database pool error:', err);

  if (client) {
    activeClients.delete(client);
    try {
      client.release(true); // Force destroy the client
    } catch (releaseError) {
      logger.error('Error releasing errored client:', releaseError as Error);
    }
  }
});

// Helper function to validate a client connection
async function validateClient(client: PoolClient): Promise<boolean> {
  try {
    await client.query('SELECT 1');
    return true;
  } catch (error) {
    logger.error('Client validation failed:', error as Error);
    return false;
  }
}

// Helper function to safely release a client
function safelyReleaseClient(client: PoolClient): void {
  if (!client || (client as any)._released) return;

  try {
    // Clear any auto-release timeout
    if ((client as any)._autoReleaseTimeout) {
      clearTimeout((client as any)._autoReleaseTimeout);
      (client as any)._autoReleaseTimeout = null;
    }

    (client as any)._released = true;
    // Release is actually synchronous despite the type definition
    client.release();
    activeClients.delete(client);
  } catch (error) {
    logger.error('Error safely releasing client:', error as Error);
    activeClients.delete(client);
  }
}

// Enhanced getClient function with read/write routing support
async function getClientImpl(forcePool?: 'read' | 'write'): Promise<PoolClient> {
  try {
    // Use the database router for intelligent pool selection
    if (forcePool || dbConfig.hasReadWriteSplitting) {
      return await getRouterClient(forcePool);
    }

    // Fallback to legacy behavior for backward compatibility
    return await pool.connect();
  } catch (error) {
    logger.error('Failed to get database connection:', error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
}

// Use the implementation as the exported function
const getClient = getClientImpl;

// Export enhanced query function for direct use
export const query = executeQuery;

// Export pools for direct access if needed
export { writePool, readPool };

// Export legacy pool as default for backward compatibility
export { pool as default, getClient };



