import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import healthCheck from '../utils/healthCheck';

/**
 * PHASE 2: Ultra-optimized request logger middleware
 * - Minimal overhead for all requests
 * - Complete removal of logging for high-traffic endpoints in production
 * - Only logs errors and critical operations
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  // PHASE 2: Complete skip for high-traffic endpoints in production
  if (process.env.NODE_ENV === 'production' && (
      req.path === '/api/domains' ||
      req.path === '/api/inboxes' && req.method === 'GET' ||
      req.path.includes('/emails') ||
      req.path === '/health' ||
      req.path === '/healthz' ||
      req.path.startsWith('/static/'))) {
    return next();
  }

  // In development, still skip some high-traffic endpoints
  if (process.env.NODE_ENV !== 'production' && (
      req.path === '/health' ||
      req.path === '/healthz' ||
      req.path.startsWith('/static/'))) {
    return next();
  }

  // PRODUCTION SAMPLING: Only log 1 in 1000 requests in production
  if (process.env.NODE_ENV === 'production' && Math.random() > 0.001) {
    return next();
  }

  // Get start time - only for requests we're actually logging
  const start = Date.now();

  // Process the request
  next();

  // Log after response is sent - using once to prevent memory leaks
  res.once('finish', () => {
    // Calculate response time
    const responseTime = Date.now() - start;

    // PRODUCTION: Only log errors and extremely slow requests
    if (process.env.NODE_ENV === 'production') {
      // Only log server errors and extremely slow requests (>1000ms)
      if (res.statusCode >= 500 || responseTime > 1000) {
        // Use minimal logging format
        const logMessage = `${req.method} ${req.path} ${res.statusCode} ${responseTime}ms`;

        if (res.statusCode >= 500) {
          logger.error(logMessage);
        } else {
          logger.warn(logMessage);
        }
      }
    }
    // DEVELOPMENT: More detailed logging but still optimized
    else {
      // Log errors and slow requests
      if (res.statusCode >= 400 || responseTime > 500) {
        logger.info(`${req.method} ${req.path} - ${res.statusCode} - ${responseTime}ms`);
      }
    }

    // Record response time in the background without blocking
    if (responseTime > 500) {
      setTimeout(() => {
        try {
          healthCheck.recordResponseTime(responseTime);
        } catch (e) {
          // Ignore errors in background processing
        }
      }, 0);
    }
  });
};
