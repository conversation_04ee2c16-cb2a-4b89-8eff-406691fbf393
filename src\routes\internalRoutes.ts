// src/routes/internalRoutes.ts
import express from 'express';
import { internalApi<PERSON>eyAuth } from '../middlewares/internalApiKeyAuth';
import * as internalController from '../controllers/internalController';
import logger from '../utils/logger';

const router = express.Router();

// IP restriction has been removed as it's now handled by Cloudflare WAF rules
logger.info('Internal routes IP restriction is now handled by Cloudflare WAF rules');

// Configure slightly higher limits for internal routes (for email attachments)
const highLimitJsonParser = express.json({ limit: '10mb' });

// Endpoint to receive emails from SMTP server
router.post('/emails', highLimitJsonParser, internalApiKeyAuth, internalController.processEmail);

// Endpoint to check if an inbox exists
router.get('/inbox/:email', internalApiKeyAuth, internalController.getInboxInfo as express.RequestHandler);

export default router;
