import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';
import logger from '../utils/logger';

// Load environment variables
dotenv.config();

// Connection health status
interface ConnectionHealth {
  isHealthy: boolean;
  lastCheck: Date;
  consecutiveFailures: number;
}

// Pool configuration interface
interface PoolConfig {
  host: string;
  user: string;
  password: string;
  database: string;
  port: number;
  ssl: any;
  max: number;
  min: number;
  idleTimeoutMillis: number;
  connectionTimeoutMillis: number;
  acquireTimeoutMillis: number;
  keepAlive: boolean;
  statement_timeout: number;
  query_timeout: number;
  application_name: string;
}

// Failover configuration
const FAILOVER_CONFIG = {
  maxRetries: parseInt(process.env.DB_MAX_RETRIES || '3'),
  retryDelays: [1000, 2000, 4000], // 1s, 2s, 4s exponential backoff
  healthCheckInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000'), // 30 seconds
  maxConsecutiveFailures: parseInt(process.env.DB_MAX_CONSECUTIVE_FAILURES || '3'),
};

// Track connection health
const writePoolHealth: ConnectionHealth = {
  isHealthy: true,
  lastCheck: new Date(),
  consecutiveFailures: 0,
};

const readPoolHealth: ConnectionHealth = {
  isHealthy: true,
  lastCheck: new Date(),
  consecutiveFailures: 0,
};

// Helper function to create pool configuration with region-aware settings
function createPoolConfig(prefix: string, fallbackPrefix: string = 'PG'): PoolConfig {
  const getEnvVar = (suffix: string) => {
    return process.env[`${prefix}_${suffix}`] || process.env[`${fallbackPrefix}${suffix}`];
  };

  // Detect if this is a high-latency region (India, etc.)
  const instanceId = process.env.INSTANCE_ID || 'unknown';
  const region = process.env.REGION || 'unknown';
  const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                              region.toLowerCase().includes('in') ||
                              instanceId.toLowerCase().includes('india') ||
                              instanceId.toLowerCase().includes('in') ||
                              // Also check if connecting to India server IP
                              getEnvVar('HOST')?.includes('*************');

  // Region-specific timeout adjustments
  const timeoutMultiplier = isHighLatencyRegion ? 2.5 : 1.0;

  // Base timeouts
  const baseConnectionTimeout = parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000');
  const baseAcquireTimeout = parseInt(process.env.DB_ACQUIRE_TIMEOUT || '5000');
  const baseStatementTimeout = parseInt(process.env.DB_STATEMENT_TIMEOUT || '45000');
  const baseQueryTimeout = parseInt(process.env.DB_QUERY_TIMEOUT || '45000');

  return {
    host: getEnvVar('HOST') || 'localhost',
    user: getEnvVar('USER') || 'postgres',
    password: getEnvVar('PASSWORD') || '',
    database: getEnvVar('DATABASE') || 'tempfly_app',
    port: parseInt(getEnvVar('PORT') || '5432'),
    ssl: (getEnvVar('SSLMODE') || process.env.PGSSLMODE) === 'disable' ? false : {
      rejectUnauthorized: false,
      requestCert: true
    },
    // Pool size adjustments for high-latency regions
    max: parseInt(process.env.DB_POOL_MAX || (isHighLatencyRegion ? '15' : '20')),
    min: parseInt(process.env.DB_POOL_MIN || (isHighLatencyRegion ? '2' : '3')),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || (isHighLatencyRegion ? '90000' : '60000')),
    // Increased timeouts for high-latency regions
    connectionTimeoutMillis: Math.round(baseConnectionTimeout * timeoutMultiplier),
    acquireTimeoutMillis: Math.round(baseAcquireTimeout * timeoutMultiplier),
    keepAlive: true,
    statement_timeout: Math.round(baseStatementTimeout * timeoutMultiplier),
    query_timeout: Math.round(baseQueryTimeout * timeoutMultiplier),
    application_name: `tempfly-api-${instanceId}-${region}`,
  };
}

// Determine if read/write splitting is configured
const hasReadWriteConfig = !!(
  process.env.DB_WRITE_HOST && 
  process.env.DB_READ_HOST
);

// Create pool configurations
const writePoolConfig = hasReadWriteConfig 
  ? createPoolConfig('DB_WRITE', 'PG')
  : createPoolConfig('PG', 'PG');

const readPoolConfig = hasReadWriteConfig 
  ? createPoolConfig('DB_READ', 'PG')
  : createPoolConfig('PG', 'PG'); // Fallback to same as write if no read config

// Create connection pools
export const writePool = new Pool(writePoolConfig);
export const readPool = hasReadWriteConfig ? new Pool(readPoolConfig) : writePool;

// Log configuration with region information
const instanceId = process.env.INSTANCE_ID || 'unknown';
const region = process.env.REGION || 'unknown';
const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                            region.toLowerCase().includes('in') ||
                            instanceId.toLowerCase().includes('india') ||
                            instanceId.toLowerCase().includes('in');

logger.info(`Database pools initialized for region: ${region}, instance: ${instanceId}`);
logger.info(`- Write pool: ${writePoolConfig.host}:${writePoolConfig.port}/${writePoolConfig.database}`);
logger.info(`- Connection timeout: ${writePoolConfig.connectionTimeoutMillis}ms`);
logger.info(`- Acquire timeout: ${writePoolConfig.acquireTimeoutMillis}ms`);
logger.info(`- Pool size: ${writePoolConfig.min}-${writePoolConfig.max} connections`);
logger.info(`- High latency region optimizations: ${isHighLatencyRegion ? 'ENABLED' : 'DISABLED'}`);

if (hasReadWriteConfig) {
  logger.info(`- Read pool: ${readPoolConfig.host}:${readPoolConfig.port}/${readPoolConfig.database}`);
  logger.info('Read/write splitting enabled');
} else {
  logger.info('- Read pool: Same as write pool (single database mode)');
  logger.info('Read/write splitting disabled - using single database');
}

// Health check function
async function checkPoolHealth(pool: Pool, poolName: string): Promise<boolean> {
  try {
    const client = await pool.connect();
    await client.query('SELECT 1 AS health_check');
    client.release();
    return true;
  } catch (error) {
    logger.error(`Health check failed for ${poolName}:`, error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

// Update health status
async function updateHealthStatus(
  pool: Pool, 
  poolName: string, 
  health: ConnectionHealth
): Promise<void> {
  const isHealthy = await checkPoolHealth(pool, poolName);
  
  if (isHealthy) {
    if (!health.isHealthy) {
      logger.info(`${poolName} pool recovered`);
    }
    health.isHealthy = true;
    health.consecutiveFailures = 0;
  } else {
    health.isHealthy = false;
    health.consecutiveFailures++;
    
    if (health.consecutiveFailures >= FAILOVER_CONFIG.maxConsecutiveFailures) {
      logger.error(`${poolName} pool marked as unhealthy after ${health.consecutiveFailures} consecutive failures`);
    }
  }
  
  health.lastCheck = new Date();
}

// Periodic health checks
setInterval(async () => {
  await Promise.all([
    updateHealthStatus(writePool, 'Write', writePoolHealth),
    hasReadWriteConfig ? updateHealthStatus(readPool, 'Read', readPoolHealth) : Promise.resolve(),
  ]);
}, FAILOVER_CONFIG.healthCheckInterval);

// Pool event handlers for write pool
writePool.on('connect', (client: PoolClient) => {
  logger.debug('Write pool client connected');
});

writePool.on('error', (err: Error, client: PoolClient) => {
  logger.error('Write pool error:', err);
  writePoolHealth.isHealthy = false;
  writePoolHealth.consecutiveFailures++;
});

// Pool event handlers for read pool (if different from write pool)
if (hasReadWriteConfig) {
  readPool.on('connect', (client: PoolClient) => {
    logger.debug('Read pool client connected');
  });

  readPool.on('error', (err: Error, client: PoolClient) => {
    logger.error('Read pool error:', err);
    readPoolHealth.isHealthy = false;
    readPoolHealth.consecutiveFailures++;
  });
}

// Export health status getters
export const getWritePoolHealth = (): ConnectionHealth => ({ ...writePoolHealth });
export const getReadPoolHealth = (): ConnectionHealth => ({ ...readPoolHealth });

// Export configuration info
export const getDatabaseConfig = () => ({
  hasReadWriteSplitting: hasReadWriteConfig,
  writeConfig: {
    host: writePoolConfig.host,
    port: writePoolConfig.port,
    database: writePoolConfig.database,
  },
  readConfig: hasReadWriteConfig ? {
    host: readPoolConfig.host,
    port: readPoolConfig.port,
    database: readPoolConfig.database,
  } : null,
});
