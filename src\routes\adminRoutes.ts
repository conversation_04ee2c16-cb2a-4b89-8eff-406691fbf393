import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { InboxModel } from '../models/Inbox';
import { adminAuth } from '../middlewares/adminAuth';
import logger from '../utils/logger';

const router = express.Router();

// Apply admin authentication middleware to all routes
router.use(adminAuth);

/**
 * GET /api/admin/maintenance/status
 * Get the status of maintenance jobs
 */
router.get('/maintenance/status', async (req, res, next) => {
  try {
    const status = await InboxModel.getCleanupJobStatus();

    // Get request ID from middleware or generate new one
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    res.status(200).json({
      'request-id': requestId,
      'message': 'Success',
      data: status
    });
  } catch (error) {
    const errorMessage = 'Error getting maintenance status:';
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error(errorMessage, errorObj);
    next(error);
  }
});

/**
 * POST /api/admin/maintenance/run-cleanup
 * Manually trigger the inbox cleanup process
 */
router.post('/maintenance/run-cleanup', async (req, res, next) => {
  try {
    const startTime = Date.now();
    const affectedRows = await InboxModel.cleanupExpired();
    const executionTime = Date.now() - startTime;

    res.status(200).json({
      status: 'success',
      message: `Cleanup completed successfully`,
      data: {
        affectedRows,
        executionTime: `${executionTime}ms`
      }
    });
  } catch (error) {
    const errorMessage = 'Error running manual cleanup:';
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error(errorMessage, errorObj);
    next(error);
  }
});

export default router;
