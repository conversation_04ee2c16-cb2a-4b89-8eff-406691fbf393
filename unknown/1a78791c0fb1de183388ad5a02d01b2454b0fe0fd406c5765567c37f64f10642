import { Request, Response, NextFunction } from 'express';
import healthCheck from '../utils/healthCheck';
import { runInBackground } from '../utils/backgroundTasks';
import logger from '../utils/logger';

/**
 * Optimized middleware to monitor request performance
 * - Skips monitoring for health checks and static resources
 * - Only logs slow requests in production
 * - Uses background processing for all metrics collection
 */
export const performanceMonitoring = (_req: Request, res: Response, next: NextFunction): void => {
  // Skip performance monitoring for health check endpoints and static resources
  if (_req.path === '/health' ||
      _req.path === '/healthz' ||
      _req.path.startsWith('/static/')) {
    return next();
  }

  // Check if this is a high-traffic endpoint
  const isHighTrafficEndpoint =
    _req.path === '/api/domains' ||
    _req.path === '/api/inboxes' ||
    _req.path.includes('/emails');

  // For high-traffic endpoints in production, use more aggressive sampling to reduce overhead
  if (process.env.NODE_ENV === 'production') {
    if (isHighTrafficEndpoint) {
      // Sample only 5% of requests for high-traffic endpoints
      if (Math.random() > 0.05) {
        // Skip performance monitoring completely for 95% of requests
        return next();
      }
    } else {
      // For other endpoints, sample 20% of requests
      if (Math.random() > 0.2) {
        return next();
      }
    }
  }

  // Record start time
  const startTime = Date.now();

  // Increment request counter - do this synchronously as it's fast
  healthCheck.incrementRequestCount();

  // Store original end method
  const originalEnd = res.end;

  // Override end method to record metrics
  res.end = function(chunk: any, encoding?: any, callback?: any): Response {
    try {
      // Calculate response time
      const responseTime = Date.now() - startTime;

      // Add response time header (only if headers haven't been sent yet)
      if (!res.headersSent) {
        res.setHeader('X-Response-Time', `${responseTime}ms`);
      }

      // Get request information for background processing
      const statusCode = res.statusCode;
      const method = _req.method;
      const path = _req.path;
      const requestId = (_req as any).requestId;

      // Only track metrics for errors or very slow requests in production
      if (process.env.NODE_ENV === 'production') {
        // In production, only track errors and very slow requests
        if (statusCode >= 400 || responseTime > 800) {
          // Use setTimeout instead of runInBackground for less overhead
          setTimeout(() => {
            try {
              // Record response time
              healthCheck.recordResponseTime(responseTime);

              // If error status code, increment error counter
              if (statusCode >= 400) {
                healthCheck.incrementErrorCount();
              }

              // Only log very slow requests
              if (responseTime > 800) {
                logger.warn(`Slow request: ${method} ${path} took ${responseTime}ms (status: ${statusCode})`);
              }
            } catch (e) {
              // Ignore errors in background processing
            }
          }, 0);
        }
        // Skip metrics for normal requests in production
      } else {
        // In development, track all metrics for debugging
        setTimeout(() => {
          try {
            // Record response time
            healthCheck.recordResponseTime(responseTime);

            // If error status code, increment error counter
            if (statusCode >= 400) {
              healthCheck.incrementErrorCount();
            }

            // Log slow requests with more details in development
            if (responseTime > 500) {
              logger.warn(`Slow request: ${method} ${path} took ${responseTime}ms (status: ${statusCode}, requestId: ${requestId})`);
            }
          } catch (e) {
            // Ignore errors in background processing
          }
        }, 0);
      }
    } catch (error) {
      // Log error but don't fail the request
      logger.error('Error in performance monitoring:', error instanceof Error ? error : new Error(String(error)));
    }

    // Call the original end method immediately
    return originalEnd.call(this, chunk, encoding, callback);
  };

  next();
};

export default performanceMonitoring;
