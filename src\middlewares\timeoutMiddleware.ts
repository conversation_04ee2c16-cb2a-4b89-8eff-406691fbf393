/**
 * Request Timeout Middleware
 * 
 * This middleware sets a timeout for each request to prevent long-running requests
 * from blocking the server. If a request takes longer than the specified timeout,
 * it will be terminated with a 408 Request Timeout response.
 */

import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * Request timeout middleware
 * @param timeout Timeout in milliseconds (default: 9000ms)
 * @returns Express middleware function
 */
export const timeoutMiddleware = (timeout: number = 9000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip for health check endpoints
    if (req.path === '/health' || req.path === '/healthz' || req.path === '/api/health') {
      return next();
    }
    
    // Get request ID for logging
    const requestId = (req as any).requestId || uuidv4().toUpperCase();
    
    // Set a timeout for the request
    const timeoutId = setTimeout(() => {
      // Only send response if it hasn't been sent yet
      if (!res.headersSent) {
        // Log the timeout
        logger.warn(`Request timeout after ${timeout}ms: ${req.method} ${req.originalUrl}`);
        
        // Send timeout response
        res.status(408).json({
          'request-id': requestId,
          'message': 'Request timeout',
          'data': null,
          'code': 408
        });
      }
    }, timeout);
    
    // Store the timeout ID on the request object
    (req as any).timeoutId = timeoutId;
    
    // Clear the timeout when the response is sent
    res.on('finish', () => {
      clearTimeout(timeoutId);
    });
    
    // Clear the timeout when the response is closed
    res.on('close', () => {
      clearTimeout(timeoutId);
    });
    
    next();
  };
};

export default timeoutMiddleware;
