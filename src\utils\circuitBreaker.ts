/**
 * Circuit Breaker Pattern Implementation
 * 
 * This utility implements the circuit breaker pattern to prevent cascading failures
 * when external services (like Redis) are experiencing issues.
 * 
 * The circuit breaker has three states:
 * - CLOSED: Normal operation, requests pass through
 * - OPEN: Service is failing, requests are immediately rejected
 * - HALF_OPEN: Testing if the service has recovered
 */

import logger from './logger';

export class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private readonly name: string,
    private readonly failureThreshold: number = 5,
    private readonly resetTimeout: number = 30000 // 30 seconds
  ) {
    logger.info(`Circuit breaker "${name}" initialized with threshold ${failureThreshold} and reset timeout ${resetTimeout}ms`);
  }
  
  /**
   * Execute a function with circuit breaker protection
   * @param fn The function to execute
   * @param fallback The fallback function to execute if the circuit is open
   * @returns The result of the function or fallback
   */
  public async execute<T>(fn: () => Promise<T>, fallback: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      // Check if it's time to try again
      const now = Date.now();
      if (now - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
        logger.debug(`Circuit "${this.name}" changed from OPEN to HALF_OPEN`);
      } else {
        // Circuit is open, use fallback
        return fallback();
      }
    }
    
    try {
      const result = await fn();
      
      // If we're in HALF_OPEN and succeeded, reset the circuit
      if (this.state === 'HALF_OPEN') {
        this.reset();
      }
      
      return result;
    } catch (error) {
      this.failures++;
      this.lastFailureTime = Date.now();
      
      // If we've reached the threshold, open the circuit
      if (this.failures >= this.failureThreshold) {
        this.state = 'OPEN';
        logger.warn(`Circuit "${this.name}" changed to OPEN after ${this.failures} failures`);
      }
      
      // Log the error
      logger.error(`Circuit "${this.name}" failure ${this.failures}/${this.failureThreshold}:`, 
        error instanceof Error ? error : new Error(String(error)));
      
      return fallback();
    }
  }
  
  /**
   * Reset the circuit breaker to closed state
   */
  public reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    logger.debug(`Circuit "${this.name}" reset to CLOSED`);
  }
  
  /**
   * Get the current state of the circuit breaker
   */
  public getState(): string {
    return this.state;
  }
  
  /**
   * Get the number of failures
   */
  public getFailures(): number {
    return this.failures;
  }
}

// Create Redis circuit breaker instance
export const redisCircuitBreaker = new CircuitBreaker('redis', 3, 60000);
