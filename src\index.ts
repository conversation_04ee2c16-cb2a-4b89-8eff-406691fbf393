import express, { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import cors from 'cors';
import compression from 'compression';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import https from 'https';
import serverConfig from './config/server';
import { errorHand<PERSON>, notFoundHandler } from './middlewares/errorHandler';
import { requestLogger } from './middlewares/requestLogger';
import { requestIdMiddleware } from './middlewares/requestId';
import { requestAuditLogger } from './middlewares/auditLoggerMiddleware';
// Performance monitoring middleware removed to reduce latency
// import performanceMonitoring from './middlewares/performanceMonitoring';
import { encryptResponsePayload, decryptRequestPayload } from './middlewares/payloadEncryption';
// PHASE 2: Replace helmet and additionalSecurityHeaders with combined middleware
import combinedSecurityHeaders from './middlewares/combinedSecurityHeaders';
import { scannerProtection } from './middlewares/scannerProtection';
import { rapidApiAuth } from './middlewares/rapidApiAuth';
import { rapidApiUsageTracker } from './middlewares/rapidApiUsageTracker';
import timeoutMiddleware from './middlewares/timeoutMiddleware';
import inboxRoutes from './routes/inboxRoutes';
import domainRoutes from './routes/domainRoutes';
import apiKeyRoutes from './routes/apiKeyRoutes';
import monitoringRoutes from './routes/monitoringRoutes';
import rapidApiRoutes from './routes/rapidApiRoutes';
import adminRoutes from './routes/adminRoutes';
// Import Redis functions from direct Redis connection
import { connectRedis, setCache, isRedisConnected } from './config/redis-direct';
import logger from './utils/logger';
import { initSmtpServer } from './services/smtpService';
import internalRoutes from './routes/internalRoutes';
import validateEnv from './utils/validateEnv';
import { DomainModel } from './models/Domain';
import { initPreparedStatements } from './config/prepared-statements';
import pool from './config/database';
import { detectSilentFailures } from './utils/errorBoundary';

// Load environment variables
dotenv.config();

// Validate environment variables
validateEnv();

// Initialize silent failure detection
detectSilentFailures();

// Create Express app
const app = express();

// Enable trust proxy to properly handle X-Forwarded-For headers
// This is required when the app is behind a proxy/load balancer in production
// Use a more specific trust proxy setting to avoid rate limiting bypass
const trustProxyIPs = ['loopback', 'linklocal', 'uniquelocal'];

// Add additional trusted IPs from environment variable
if (process.env.TRUSTED_PROXY_IPS) {
  const additionalIPs = process.env.TRUSTED_PROXY_IPS.split(',').map(ip => ip.trim());
  trustProxyIPs.push(...additionalIPs);
}

app.set('trust proxy', trustProxyIPs);

// Redis connection is now handled later in the file when starting the server

// Create logs directory if it doesn't exist
try {
  const logsDir = path.resolve(path.join(process.cwd(), 'logs'));

  // Ensure the logs directory is within the application directory
  if (!logsDir.startsWith(process.cwd())) {
    logger.error('Security warning: Attempted to create logs directory outside of application directory');
  } else if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true, mode: 0o755 }); // Set proper permissions
    logger.info(`Created logs directory at ${logsDir}`);
  }
} catch (error) {
  logger.error('Failed to create logs directory:', error as Error);
}

// PHASE 2: Streamlined middleware chain for better performance
app.use(requestIdMiddleware); // Generate request ID - keep first as other middleware depends on it
app.use(timeoutMiddleware()); // Add request timeout protection with unified timeout configuration
app.use(combinedSecurityHeaders); // PHASE 2: Combined security headers (replaces helmet + additionalSecurityHeaders)
app.use(scannerProtection); // Protection against scanning/probing attacks - should be early

// Add ultra-optimized compression middleware - should be early to compress all responses
app.use(compression({
  // Compress all responses (no threshold)
  threshold: 0,
  // Use balanced compression level for better performance
  level: 4,
  // Disable memory cache to reduce memory usage
  memLevel: 7,
  // Use smaller chunks for faster compression
  chunkSize: 16 * 1024,
  filter: (req: express.Request, res: express.Response) => {
    // Don't compress responses for clients that don't support it
    if (req.headers['x-no-compression']) {
      return false;
    }
    // Don't compress already compressed content types
    const contentType = res.getHeader('Content-Type') as string;
    if (contentType && (
      contentType.includes('image/') ||
      contentType.includes('application/pdf') ||
      contentType.includes('application/zip') ||
      contentType.includes('application/gzip') ||
      contentType.includes('application/x-compressed') ||
      contentType.includes('application/x-zip-compressed')
    )) {
      return false;
    }
    // Use compression for all other requests
    return compression.filter(req, res);
  }
}));

// Performance monitoring middleware removed to reduce latency
// app.use(performanceMonitoring); // Performance monitoring - REMOVED
app.use(requestLogger); // Request logging
app.use(requestAuditLogger); // Audit logging
app.use(rapidApiUsageTracker); // Track RapidAPI usage
// Configure CORS to only allow requests from RapidAPI and trusted domains
app.use(cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps, curl, etc)
    if (!origin) return callback(null, true);

    // List of allowed origins from environment variable or defaults
    const allowedOrigins = [
      // RapidAPI domains
      'https://rapidapi.com',
      'https://api.rapidapi.com',
    ];

    // Add additional allowed origins from environment variable
    if (process.env.CORS_ALLOWED_ORIGINS) {
      const additionalOrigins = process.env.CORS_ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
      allowedOrigins.push(...additionalOrigins);
    }

    // Allow localhost in development
    if (process.env.NODE_ENV !== 'production') {
      // Get development ports from environment or use defaults
      const devPorts = (process.env.DEV_PORTS || '3000,8080').split(',').map(port => port.trim());
      devPorts.forEach(port => {
        allowedOrigins.push(`http://localhost:${port}`);
      });
    }

    // Check if the origin is allowed
    if (allowedOrigins.indexOf(origin) !== -1 || origin.endsWith('.rapidapi.com')) {
      callback(null, true);
    } else {
      logger.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-API-Key', 'X-RapidAPI-Key', 'X-RapidAPI-Host'],
  credentials: true,
  maxAge: 86400 // 24 hours
})); // CORS
app.use(express.json({ limit: '5mb' })); // Parse JSON bodies with reasonable limit
app.use(express.urlencoded({ extended: true, limit: '5mb' })); // Parse URL-encoded bodies with reasonable limit
app.use(decryptRequestPayload); // Decrypt sensitive fields in request
app.use(encryptResponsePayload); // Encrypt sensitive fields in response

// Set performance-related headers
app.use((req, res, next) => {
  // Set cache-control headers based on endpoint
  if (req.path === '/api/domains') {
    res.set('Cache-Control', 'public, max-age=86400'); // 24 hours
  } else if (req.path.startsWith('/api/inboxes') && req.method === 'GET') {
    res.set('Cache-Control', 'private, max-age=60'); // 1 minute
  } else {
    res.set('Cache-Control', 'no-store');
  }

  // Set security headers
  res.set('X-Content-Type-Options', 'nosniff');

  next();
});

// Health check route with optional RapidAPI authentication
app.get('/health', rapidApiAuth, async (_req, res) => {
  try {
    // Check database connection with timeout
    let dbStatus = 'error';
    try {
      // Create a promise that resolves when the database query completes
      const dbCheckPromise = new Promise(async (resolve, reject) => {
        try {
          const client = await pool.connect();
          await client.query('SELECT 1');
          client.release();
          resolve('ok');
        } catch (err) {
          reject(err);
        }
      });

      // Create a promise that rejects after a timeout using unified config
      const timeoutPromise = new Promise((_, reject) => {
        const { TIMEOUTS } = require('./config/timeouts');
        setTimeout(() => reject(new Error('Database connection timeout')), TIMEOUTS.HEALTH_CHECK_QUICK);
      });

      // Race the database check against the timeout
      dbStatus = await Promise.race([dbCheckPromise, timeoutPromise]) as string;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Database health check failed: ${errorMessage}`);
      dbStatus = `error: ${errorMessage}`;
    }

    // Check Redis connection with timeout
    let redisStatus = 'unknown';
    try {
      // Create a promise that resolves when the Redis check completes
      const redisCheckPromise = new Promise((resolve, reject) => {
        if (isRedisConnected()) {
          resolve('ok');
        } else {
          reject(new Error('Redis not connected'));
        }
      });

      // Create a promise that rejects after a timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Redis check timeout')), 1000);
      });

      // Race the Redis check against the timeout
      redisStatus = await Promise.race([redisCheckPromise, timeoutPromise]) as string;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Redis health check failed: ${errorMessage}`);
      redisStatus = `error: ${errorMessage}`;
    }

    // Get instance ID
    const instanceId = process.env.INSTANCE_ID || 'unknown';

    // Get memory usage
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    };

    // Get database pool information
    let poolInfo = {};
    try {
      poolInfo = {
        totalCount: pool.totalCount,
        idleCount: pool.idleCount,
        waitingCount: pool.waitingCount,
      };
    } catch (poolError) {
      logger.error('Error getting pool info', poolError instanceof Error ? poolError : new Error(String(poolError)));
    }

    // Return health status using send instead of json to avoid middleware issues
    res.status(200).send({
      status: 'ok',
      timestamp: new Date().toISOString(),
      instance: instanceId,
      uptime: process.uptime(),
      memory: memoryUsageMB,
      services: {
        database: dbStatus,
        redis: redisStatus,
        pool: poolInfo
      },
    });
  } catch (error) {
    logger.error('Health check failed', error instanceof Error ? error : new Error(String(error)));
    // Use send instead of json to avoid middleware issues
    res.status(500).send({
      status: 'error',
      message: 'Health check failed',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Enhanced readiness endpoint for deployment health checks
app.get('/api/internal/readiness', async (_req: Request, res: Response): Promise<void> => {
  try {
    const startTime = Date.now();
    const instanceId = process.env.INSTANCE_ID || 'unknown';
    const checks: Record<string, any> = {};

    // Quick database connection check with timeout
    try {
      const dbCheckPromise = new Promise<string>(async (resolve, reject) => {
        try {
          const client = await pool.connect();
          await client.query('SELECT 1 AS readiness_check');
          client.release();
          resolve('ready');
        } catch (err) {
          reject(err);
        }
      });

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Database readiness timeout')), 2000);
      });

      await Promise.race([dbCheckPromise, timeoutPromise]);
      checks.database = { status: 'ready', responseTime: Date.now() - startTime };
    } catch (error) {
      checks.database = {
        status: 'not_ready',
        error: error instanceof Error ? error.message : String(error),
        responseTime: Date.now() - startTime
      };
      res.status(503).json({
        status: 'not_ready',
        instance: instanceId,
        checks,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Quick Redis check if enabled
    if (process.env.REDIS_ENABLED === 'true') {
      try {
        if (isRedisConnected()) {
          checks.redis = { status: 'ready' };
        } else {
          checks.redis = { status: 'not_ready', error: 'Redis not connected' };
        }
      } catch (error) {
        checks.redis = {
          status: 'not_ready',
          error: error instanceof Error ? error.message : String(error)
        };
      }
    } else {
      checks.redis = { status: 'disabled' };
    }

    const totalTime = Date.now() - startTime;
    res.status(200).json({
      status: 'ready',
      instance: instanceId,
      checks,
      responseTime: totalTime,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error : new Error(String(error));
    logger.error('Readiness check failed', errorMessage);
    res.status(503).json({
      status: 'not_ready',
      instance: process.env.INSTANCE_ID || 'unknown',
      error: errorMessage.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Simple health check route for container health checks (no authentication required)
app.get('/healthz', async (_req, res) => {
  try {
    // Get instance ID
    const instanceId = process.env.INSTANCE_ID || 'unknown';

    // Quick database check with region-aware timeout
    let dbStatus = 'unknown';
    try {
      // Determine timeout based on region/instance
      const instanceId = process.env.INSTANCE_ID || 'unknown';
      const region = process.env.REGION || 'unknown';

      // Use longer timeout for India and other high-latency regions
      const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                                  region.toLowerCase().includes('in') ||
                                  instanceId.toLowerCase().includes('india') ||
                                  instanceId.toLowerCase().includes('in');

      const { TIMEOUTS } = require('./config/timeouts');
      const healthCheckTimeout = TIMEOUTS.HEALTH_CHECK_FULL; // Use unified timeout config

      // Create a promise that resolves when the database query completes
      const dbCheckPromise = new Promise(async (resolve, reject) => {
        let client;
        try {
          const startTime = Date.now();
          client = await pool.connect();
          await client.query('SELECT 1');
          const responseTime = Date.now() - startTime;

          // Log slow connections for monitoring
          if (responseTime > 1000) {
            console.warn(`Slow database connection: ${responseTime}ms (region: ${region}, instance: ${instanceId})`);
          }

          resolve('ok');
        } catch (err) {
          reject(err);
        } finally {
          if (client) {
            try {
              client.release();
            } catch (releaseErr) {
              console.error('Error releasing database client:', releaseErr);
            }
          }
        }
      });

      // Create a promise that rejects after a region-aware timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          const errorMsg = `Database connection timeout after ${healthCheckTimeout}ms (region: ${region}, instance: ${instanceId})`;
          reject(new Error(errorMsg));
        }, healthCheckTimeout);
      });

      // Race the database check against the timeout
      dbStatus = await Promise.race([dbCheckPromise, timeoutPromise]) as string;
    } catch (error) {
      dbStatus = 'error';

      // Enhanced error logging for timeout issues
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isTimeout = errorMessage.includes('timeout');

      if (isTimeout) {
        // Always log timeout errors as they indicate infrastructure issues
        console.error('Database health check timeout:', {
          error: errorMessage,
          instance: process.env.INSTANCE_ID,
          region: process.env.REGION,
          timestamp: new Date().toISOString()
        });
      } else if (process.env.NODE_ENV !== 'production') {
        console.error('Database health check failed:', error);
      }
    }

    // Return basic health status without using res.json to avoid middleware issues
    res.status(200).send({
      status: 'ok',
      timestamp: new Date().toISOString(),
      instance: instanceId,
      message: 'Service is running',
      database: dbStatus
    });
  } catch (error) {
    // Handle any errors directly without going through the error middleware
    console.error('Health check error:', error);
    res.status(500).send({
      status: 'error',
      timestamp: new Date().toISOString(),
      message: 'Health check failed'
    });
  }
});

// Warm-up endpoint for reducing cold start latency (no authentication required)
app.get('/api/internal/warmup', async (_req, res) => {
  try {
    const startTime = Date.now();
    const results: Record<string, any> = {};

    // 1. Warm up database connection
    try {
      const client = await pool.connect();
      await client.query('SELECT 1 AS connection_test');
      client.release();
      results.database = { status: 'ok', message: 'Database connection successful' };
    } catch (dbError) {
      results.database = {
        status: 'error',
        message: dbError instanceof Error ? dbError.message : String(dbError)
      };
    }

    // 2. Warm up Redis connection if enabled
    if (isRedisConnected()) {
      try {
        const pingKey = `warmup:ping:${Date.now()}`;
        await setCache(pingKey, 'pong', 5); // Short TTL of 5 seconds
        results.redis = { status: 'ok', message: 'Redis connection successful' };
      } catch (redisError) {
        results.redis = {
          status: 'error',
          message: redisError instanceof Error ? redisError.message : String(redisError)
        };
      }
    } else {
      results.redis = { status: 'disabled', message: 'Redis is not enabled or connected' };
    }

    // 3. Warm up domain cache
    try {
      const domains = await DomainModel.getAll();
      results.domains = {
        status: 'ok',
        message: `Domain cache warmed up with ${domains.length} domains`
      };
    } catch (domainError) {
      results.domains = {
        status: 'error',
        message: domainError instanceof Error ? domainError.message : String(domainError)
      };
    }

    // Calculate total warm-up time
    const duration = Date.now() - startTime;

    // Get request ID or generate new one
    const requestId = uuidv4().toUpperCase();

    // Return warm-up results
    res.status(200).json({
      'request-id': requestId,
      'message': 'Warm-up completed successfully',
      data: {
        timestamp: new Date().toISOString(),
        instance: process.env.INSTANCE_ID || 'unknown',
        duration_ms: duration,
        results
      }
    });
  } catch (error) {
    logger.error('Warm-up error:', error instanceof Error ? error : new Error(String(error)));

    // Get request ID or generate new one
    const requestId = uuidv4().toUpperCase();

    res.status(500).json({
      'request-id': requestId,
      'message': 'Warm-up failed',
      data: {
        error: error instanceof Error ? error.message : String(error)
      }
    });
  }
});

// API Routes
app.use('/api/inboxes', inboxRoutes);
app.use('/api/domains', domainRoutes);
app.use('/api/keys', apiKeyRoutes);
// Test routes have been completely removed for security
logger.info('Test routes are permanently disabled for security');
app.use('/api/internal', internalRoutes);
app.use('/api/monitoring', monitoringRoutes);
app.use('/api/rapidapi', rapidApiRoutes);
app.use('/api/admin', adminRoutes);

// Database diagnostics routes (admin only)
import diagnosticsRoutes from './routes/diagnostics';
app.use('/api/diagnostics', diagnosticsRoutes);

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Configure HTTPS if certificates are available
let server;
if (process.env.NODE_ENV === 'production' && process.env.SSL_KEY_PATH && process.env.SSL_CERT_PATH) {
  try {
    // Validate paths to prevent path traversal attacks
    const keyPath = path.resolve(process.env.SSL_KEY_PATH);
    const certPath = path.resolve(process.env.SSL_CERT_PATH);

    // Check if files exist before reading
    if (!fs.existsSync(keyPath)) {
      throw new Error(`SSL key file not found: ${keyPath}`);
    }

    if (!fs.existsSync(certPath)) {
      throw new Error(`SSL certificate file not found: ${certPath}`);
    }

    // Read SSL certificates
    const privateKey = fs.readFileSync(keyPath, 'utf8');
    const certificate = fs.readFileSync(certPath, 'utf8');
    const credentials = { key: privateKey, cert: certificate };

    // Create HTTPS server
    server = https.createServer(credentials, app);
    server.listen(serverConfig.port, () => {
      logger.info(`HTTPS server running in ${serverConfig.nodeEnv} mode on port ${serverConfig.port}`);
    });
  } catch (error) {
    logger.error('Failed to start HTTPS server:', error as Error);
    logger.warn('Falling back to HTTP server');

    // Fall back to HTTP server if HTTPS setup fails
    server = app.listen(serverConfig.port, () => {
      logger.info(`HTTP server running in ${serverConfig.nodeEnv} mode on port ${serverConfig.port} (HTTPS fallback)`);
    });
  }
} else {
  // Connect to Redis if enabled
  if (process.env.REDIS_ENABLED === 'true') {
    // Check if we're using direct Redis connection
    if (process.env.REDIS_HOST && process.env.REDIS_PORT && process.env.REDIS_PASSWORD) {
      // Set Redis API URL and key for compatibility with existing code
      if (!process.env.REDIS_API_URL) {
        process.env.REDIS_API_URL = `http://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`;
        logger.info(`Setting REDIS_API_URL to ${process.env.REDIS_API_URL} for compatibility`);
      }

      if (!process.env.REDIS_API_KEY) {
        process.env.REDIS_API_KEY = process.env.REDIS_PASSWORD;
        logger.info('Setting REDIS_API_KEY to REDIS_PASSWORD for compatibility');
      }
    }

    // Initialize prepared statements for better database performance
    initPreparedStatements()
      .then(() => {
        logger.info('Prepared statements initialized successfully');
      })
      .catch((error) => {
        logger.warn('Failed to initialize prepared statements:', error);
      });

    connectRedis()
      .then(async (connected) => {
        if (connected) {
          logger.info('Redis connected successfully');

          // Enhanced cache warming strategy with deployment awareness
          try {
            const instanceId = parseInt(process.env.INSTANCE_ID || '1');
            const warmupDelay = parseInt(process.env.CACHE_WARMUP_DELAY || '15000');
            const warmupEnabled = process.env.CACHE_WARMUP_ENABLED !== 'false';

            if (!warmupEnabled) {
              logger.info('Cache warmup disabled');
              return;
            }

            // Stagger cache warming based on instance ID to prevent resource contention
            const staggerDelay = (instanceId - 1) * 5000; // 5 second stagger between instances
            const totalDelay = warmupDelay + staggerDelay;

            logger.info(`Scheduling cache warmup in ${totalDelay}ms (instance ${instanceId})`);

            setTimeout(async () => {
              try {
                logger.info('Starting cache warmup...');
                const startTime = Date.now();
                const domains = await DomainModel.getAll();

                if (domains.length > 0) {
                  // Format the response once
                  const domainResponse = {
                    'data': domains.map(domain => ({ domain: domain.domain }))
                  };

                  // Set in memory cache with 7-day TTL
                  const cacheTTL = 604800; // 7 days
                  const memoryCache = require('./middlewares/cacheMiddleware').memoryCache;
                  memoryCache['domains:all'] = {
                    data: domainResponse,
                    expiry: Date.now() + (cacheTTL * 1000) // 7 days
                  };

                  // Set in Redis with timeout protection
                  const cacheTimeout = parseInt(process.env.CACHE_WARMUP_TIMEOUT || '30000');
                  await Promise.race([
                    setCache('domains:all', domains, cacheTTL),
                    new Promise((_, reject) =>
                      setTimeout(() => reject(new Error('Cache warmup timeout')), cacheTimeout)
                    )
                  ]);

                  const totalTime = Date.now() - startTime;
                  logger.info(`Cache warmed up with ${domains.length} domains in ${totalTime}ms (instance ${instanceId})`);
                } else {
                  logger.warn('No domains found for cache warmup');
                }
              } catch (error) {
                logger.error('Error warming up cache:', error instanceof Error ? error : new Error(String(error)));
              }
            }, totalDelay);

          } catch (error) {
            logger.error('Error setting up cache warmup:', error instanceof Error ? error : new Error(String(error)));
          }
        } else {
          logger.warn('Redis connection failed or disabled. Continuing without Redis.');
        }
      })
      .catch((error) => {
        logger.error('Error connecting to Redis:', error);
        logger.warn('Continuing without Redis.');
      });
  } else {
    logger.info('Redis disabled. Set REDIS_ENABLED=true to enable it.');
  }

  // Start the HTTP server
  server = app.listen(serverConfig.port, () => {
    logger.info(`HTTP server running in ${serverConfig.nodeEnv} mode on port ${serverConfig.port}`);

    // Implement connection warm-up to reduce cold start latency
    setupConnectionWarmup();
  });
}

// Start the SMTP server if enabled and not in test mode
if (process.env.ENABLE_SMTP_SERVER === 'true' && process.env.NODE_ENV !== 'test') {
  // Check if required SMTP variables are set
  if (!process.env.SMTP_HOST) {
    logger.debug('SMTP_HOST not set. SMTP server will not be started.');
  } else {
    try {
      const smtpServer = initSmtpServer();
      logger.info('SMTP server initialized successfully');

      // Handle graceful shutdown
      const gracefulShutdown = async (signal: string) => {
        logger.info(`${signal} received, initiating graceful shutdown...`);

        const shutdownTimeout = parseInt(process.env.GRACEFUL_SHUTDOWN_TIMEOUT || '30000');
        const shutdownTimer = setTimeout(() => {
          logger.error('Graceful shutdown timeout, forcing exit');
          process.exit(1);
        }, shutdownTimeout);

        try {
          // Stop accepting new connections
          if (server) {
            await new Promise<void>((resolve) => {
              server.close(() => {
                logger.info('HTTP/HTTPS server closed');
                resolve();
              });
            });
          }

          // Close SMTP server
          if (smtpServer) {
            await new Promise<void>((resolve) => {
              smtpServer.close(() => {
                logger.info('SMTP server closed');
                resolve();
              });
            });
          }

          // Close database connections
          await pool.end();
          logger.info('Database connections closed');

          // Close Redis connections
          if (isRedisConnected()) {
            const { disconnectRedis } = await import('./config/redis-direct');
            await disconnectRedis();
            logger.info('Redis connections closed');
          }

          clearTimeout(shutdownTimer);
          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          const shutdownError = error instanceof Error ? error : new Error(String(error));
          logger.error('Error during graceful shutdown:', shutdownError);
          clearTimeout(shutdownTimer);
          process.exit(1);
        }
      };

      process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
      process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    } catch (error) {
      logger.error('Failed to initialize SMTP server', error as Error);
      logger.info('Continuing without SMTP server');
    }
  }
} else {
  logger.info('SMTP server disabled. Set ENABLE_SMTP_SERVER=true to enable it.');
}

// Note: Unhandled rejection and uncaught exception handlers are now managed by errorBoundary.detectSilentFailures()

/**
 * Setup periodic connection warm-up to reduce cold start latency
 * PHASE 1: Optimized warm-up frequency for better performance
 */
function setupConnectionWarmup() {
  // PHASE 1: Reduced warm-up frequency from 20 seconds to 60 seconds
  const warmupInterval = parseInt(process.env.CONNECTION_WARMUP_INTERVAL || '60000');

  // Skip in test mode
  if (process.env.NODE_ENV === 'test') {
    return;
  }

  logger.info(`PHASE 1: Setting up connection warm-up every ${warmupInterval}ms (reduced frequency for better performance)`);

  // Set up interval for periodic warm-up
  setInterval(async () => {
    try {
      // 1. Warm up database connection
      try {
        const client = await pool.connect();
        await client.query('SELECT 1 AS connection_test');
        client.release();
        logger.debug('Database connection warm-up successful');
      } catch (dbError) {
        logger.debug('Database connection warm-up failed:', dbError instanceof Error ? dbError.message : String(dbError));
      }

      // 2. Warm up Redis connection if enabled
      if (isRedisConnected()) {
        try {
          const pingKey = `warmup:ping:${Date.now()}`;
          await setCache(pingKey, 'pong', 5); // Short TTL of 5 seconds
          logger.debug('Redis connection warm-up successful');
        } catch (redisError) {
          logger.debug('Redis connection warm-up failed:', redisError instanceof Error ? redisError.message : String(redisError));
        }
      }

      // 3. Warm up domain cache periodically (less frequently)
      const currentMinute = Math.floor(Date.now() / 60000);
      if (currentMinute % 30 === 0) { // Every 30 minutes instead of 5
        // Only warm up if this is the first instance
        const instanceId = parseInt(process.env.INSTANCE_ID || '1');
        if (instanceId === 1) {
          try {
            const domains = await DomainModel.getAll();
            logger.debug(`Domain cache warm-up successful with ${domains.length} domains`);
          } catch (domainError) {
            logger.debug('Domain cache warm-up failed:', domainError instanceof Error ? domainError.message : String(domainError));
          }
        }
      }
    } catch (error) {
      // Catch any errors to prevent the interval from stopping
      logger.debug('Connection warm-up error:', error instanceof Error ? error.message : String(error));
    }
  }, warmupInterval);
}
