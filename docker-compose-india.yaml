services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "0.8"  # Reduced for single-CPU India server
          memory: 2g   # Reduced memory allocation
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 30s  # Increased for India server
      timeout: 15s   # Increased timeout for India
      retries: 5     # More retries for India
      start_period: 45s  # Longer start period for India
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=india-1
      - REGION=india
      # Connect to PostgreSQL on VPS host (from Docker container)
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=4wyWCAAk92hkGUhdh7
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      # India-specific database connection settings (higher timeouts)
      - DB_CONNECTION_TIMEOUT=25000  # 25s for India (vs 10s default)
      - DB_ACQUIRE_TIMEOUT=12500     # 12.5s for India (vs 5s default)
      - DB_STATEMENT_TIMEOUT=112500  # 112.5s for India (vs 45s default)
      - DB_QUERY_TIMEOUT=112500      # 112.5s for India (vs 45s default)
      - DB_POOL_MAX=8                # Further reduced for single-CPU server
      - DB_POOL_MIN=1                # Minimal connections for single-CPU
      - DB_IDLE_TIMEOUT=90000        # 90s idle timeout for India
      # Health check settings for India
      - DB_HEALTH_CHECK_INTERVAL=45000  # 45s health check interval
      - DB_MAX_CONSECUTIVE_FAILURES=5   # Allow more failures before marking unhealthy
      - DB_MAX_RETRIES=5                # More retries for India
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      - REDIS_CONNECT_TIMEOUT=10000     # Increased Redis timeout for India
      - REDIS_COMMAND_TIMEOUT=8000      # Increased Redis command timeout
      # Application settings optimized for single-CPU India server
      - CONNECTION_WARMUP_INTERVAL=60000  # 60s warmup interval for India
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-150}  # Reduced cache for single-CPU
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-750}  # Reduced for single-CPU
      # Logging settings for India (more verbose for debugging)
      - LOG_LEVEL=info
      - ENABLE_REQUEST_LOGGING=true
      - ENABLE_PERFORMANCE_MONITORING=true  # Enable for India debugging
      # Rate limiting adjusted for single-CPU India server
      - RATE_LIMIT_MAX_REQUESTS=200         # Further reduced for single-CPU server
      - RATE_LIMIT_WINDOW_MS=60000
      # Other environment variables
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-15}  # More log files for India debugging
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-10}    # Larger log files for India
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "0.2"  # Minimal CPU for Redis on single-CPU server
          memory: 512m # Reduced memory for Redis
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 45s  # Increased for India
      timeout: 10s   # Increased timeout
      retries: 5     # More retries
      start_period: 15s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
