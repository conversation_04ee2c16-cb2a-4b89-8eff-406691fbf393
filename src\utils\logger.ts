import fs from 'fs';
import path from 'path';
import serverConfig from '../config/server';

// Get instance ID from environment variable (set in docker-compose)
const instanceId = process.env.INSTANCE_ID || 'main';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Log file paths
const errorLogPath = path.join(logsDir, 'error.log');
const accessLogPath = path.join(logsDir, 'access.log');

// PHASE 3: Circular buffer for log queue to prevent memory leaks
class CircularLogBuffer {
  private buffer: string[];
  private head: number = 0;
  private tail: number = 0;
  private size: number = 0;
  private readonly capacity: number;

  constructor(capacity: number = 100) {
    this.capacity = capacity;
    this.buffer = new Array(capacity);
  }

  push(message: string): void {
    this.buffer[this.tail] = message;
    this.tail = (this.tail + 1) % this.capacity;

    if (this.size < this.capacity) {
      this.size++;
    } else {
      // Buffer is full, move head forward (overwrite oldest)
      this.head = (this.head + 1) % this.capacity;
    }
  }

  flush(): string[] {
    if (this.size === 0) return [];

    const logs: string[] = [];
    let current = this.head;

    for (let i = 0; i < this.size; i++) {
      logs.push(this.buffer[current]);
      current = (current + 1) % this.capacity;
    }

    // Reset buffer
    this.head = 0;
    this.tail = 0;
    this.size = 0;

    return logs;
  }

  getSize(): number {
    return this.size;
  }
}

// PHASE 3: Use circular buffer instead of growing array
const logBuffer = new CircularLogBuffer(100);
const MAX_QUEUE_SIZE = 100;
const FLUSH_INTERVAL = 5000; // 5 seconds

/**
 * Format a log message with timestamp, instance ID, and request ID if available
 */
const formatLogMessage = (level: string, message: string, requestId?: string): string => {
  const timestamp = new Date().toISOString();
  const requestIdStr = requestId ? ` [${requestId}]` : '';
  return `[${timestamp}] [${level}] [${instanceId}]${requestIdStr} ${message}\n`;
};

/**
 * Queue a log message for batched writing - PHASE 3: Using circular buffer
 */
function queueLog(filePath: string, message: string): void {
  logBuffer.push(message);

  if (logBuffer.getSize() >= MAX_QUEUE_SIZE) {
    flushLogQueue(filePath);
  }
}

/**
 * Flush the log queue to disk - PHASE 3: Using circular buffer
 */
function flushLogQueue(filePath: string): void {
  const logs = logBuffer.flush();
  if (logs.length === 0) return;

  const logContent = logs.join('');

  // PHASE 3: Use async file writing to prevent blocking
  fs.appendFile(filePath, logContent, (err) => {
    if (err) console.error(`Failed to write to log file: ${err.message}`);
  });
}

// PHASE 3: Set up periodic flushing using circular buffer
setInterval(() => {
  if (logBuffer.getSize() > 0) {
    flushLogQueue(accessLogPath);
  }
}, FLUSH_INTERVAL);

/**
 * Write to log file asynchronously to avoid blocking the event loop
 * This significantly improves performance by not blocking the main thread
 */
const writeToLog = (filePath: string, message: string): void => {
  // In production, use batched log writing
  if (process.env.NODE_ENV === 'production') {
    queueLog(filePath, message);
  } else {
    // In development, use synchronous writes for easier debugging
    fs.appendFileSync(filePath, message);
  }
};

/**
 * Log levels with performance optimizations
 */
export const logger = {
  /**
   * Log info message - PHASE 2: Optimized for production with minimal overhead
   */
  info: (message: string, requestId?: string): void => {
    // PHASE 2: Aggressive filtering for production - only critical messages
    if (process.env.NODE_ENV === 'production') {
      // PHASE 2: Only log critical system events in production
      const isCriticalMessage = /Error|Failed|Warning|server running|initialized|started|stopped|PHASE/i.test(message);

      // Skip all non-critical messages in production
      if (!isCriticalMessage) {
        return;
      }

      console.log(`[INFO] ${message}`);

      // PHASE 2: Only write critical logs to file in production
      setImmediate(() => {
        const formattedMessage = formatLogMessage('INFO', message, requestId);
        writeToLog(accessLogPath, formattedMessage);
      });
    } else {
      // Development logging remains unchanged
      const formattedMessage = formatLogMessage('INFO', message, requestId);
      console.log(formattedMessage.trim());
      writeToLog(accessLogPath, formattedMessage);
    }
  },

  /**
   * Log warning message - optimized for production
   */
  warn: (message: string, requestId?: string): void => {
    // In production, minimize string operations
    if (process.env.NODE_ENV === 'production') {
      console.warn(`[WARN] ${message}`);

      // Always write warnings to log file
      setImmediate(() => {
        const formattedMessage = formatLogMessage('WARN', message, requestId);
        writeToLog(accessLogPath, formattedMessage);
      });
    } else {
      // In development, use full formatting
      const formattedMessage = formatLogMessage('WARN', message, requestId);
      console.warn(formattedMessage.trim());
      writeToLog(accessLogPath, formattedMessage);
    }
  },

  /**
   * Log error message - optimized but comprehensive for errors
   */
  error: (message: string, error?: Error, requestId?: string): void => {
    // Always log errors to console
    console.error(`[ERROR] ${message}`);

    // Asynchronously handle detailed error logging
    setImmediate(() => {
      const formattedMessage = formatLogMessage('ERROR', message, requestId);

      // Log stack trace if available
      if (error?.stack) {
        const stackMessage = formatLogMessage('STACK', error.stack, requestId);

        // Always log stack to file
        writeToLog(errorLogPath, formattedMessage + stackMessage);

        // Only log stack to console in development
        if (serverConfig.isDevelopment) {
          console.error(error.stack);
        }
      } else {
        writeToLog(errorLogPath, formattedMessage);
      }
    });
  },

  /**
   * Log HTTP request - optimized for high throughput
   */
  http: (req: any, res: any, responseTime?: number): void => {
    // In production, only log slow responses or errors
    if (process.env.NODE_ENV === 'production') {
      const statusCode = res.statusCode;

      // Only log errors (4xx, 5xx) or slow responses (>500ms)
      if (statusCode >= 400 || (responseTime && responseTime > 500)) {
        const { method, originalUrl } = req;
        console.log(`[HTTP] ${method} ${originalUrl} ${statusCode} - ${responseTime}ms`);

        // Asynchronously write to log file
        setImmediate(() => {
          const { ip } = req;
          const userAgent = req.get('user-agent') || '-';
          const timeString = responseTime ? ` - ${responseTime}ms` : '';
          const requestId = res.get('X-Request-ID') || undefined;

          const message = `${method} ${originalUrl} ${statusCode} - ${ip} - ${userAgent}${timeString}`;
          const formattedMessage = formatLogMessage('HTTP', message, requestId);

          writeToLog(accessLogPath, formattedMessage);
        });
      }
    } else {
      // In development, log all requests with full details
      const { method, originalUrl, ip } = req;
      const statusCode = res.statusCode;
      const userAgent = req.get('user-agent') || '-';
      const timeString = responseTime ? ` - ${responseTime}ms` : '';
      const requestId = res.get('X-Request-ID') || undefined;

      const message = `${method} ${originalUrl} ${statusCode} - ${ip} - ${userAgent}${timeString}`;
      const formattedMessage = formatLogMessage('HTTP', message, requestId);

      console.log(formattedMessage.trim());
      writeToLog(accessLogPath, formattedMessage);
    }
  },

  /**
   * Log debug message (only in development)
   */
  debug: (message: string, requestId?: string): void => {
    // Only log in development mode
    if (process.env.NODE_ENV !== 'production' && serverConfig.isDevelopment) {
      const formattedMessage = formatLogMessage('DEBUG', message, requestId);
      console.debug(formattedMessage.trim());
      writeToLog(accessLogPath, formattedMessage);
    }
    // Do nothing in production
  },
};

export default logger;
