import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import logger from '../utils/logger';

/**
 * Internal API Key authentication middleware
 * Used for secure communication between the SMTP server and the main API
 */
export const internalApiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get API key from header
    const apiKey = req.headers['x-api-key'] as string;

    // Debug log for instance 2 or if DEBUG_AUTH is enabled
    if (process.env.INSTANCE_ID === '2' || process.env.DEBUG_AUTH === 'true') {
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Internal API Key Auth Check:`);
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Request API key present: ${!!apiKey}`);
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Request API key: ${apiKey}`);
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Expected INTERNAL_API_KEY: ${process.env.INTERNAL_API_KEY}`);
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Keys match: ${apiKey === process.env.INTERNAL_API_KEY}`);
    }

    // Check if API key is provided
    if (!apiKey) {
      logger.warn(`[Instance ${process.env.INSTANCE_ID}] Missing internal API key`);
      return next(new AppError('API key is required', 401));
    }

    // Validate against the internal API key from environment variables
    if (!process.env.INTERNAL_API_KEY) {
      logger.error(`[Instance ${process.env.INSTANCE_ID}] INTERNAL_API_KEY environment variable is not set`);
      return next(new AppError('Server configuration error', 500));
    }

    if (apiKey !== process.env.INTERNAL_API_KEY) {
      logger.warn(`[Instance ${process.env.INSTANCE_ID}] Invalid internal API key attempt`);
      return next(new AppError('Invalid API key', 401));
    }

    // Continue to next middleware
    next();
  } catch (error) {
    next(error);
  }
};
